package dto

import (
	"time"
)

// FollowUserRequest 关注用户请求
type FollowUserRequest struct {
	FolloweeKSUID string `json:"followee_ksuid" binding:"required" validate:"required"` // 被关注者的用户KSUID
}

// UnfollowUserRequest 取消关注用户请求
type UnfollowUserRequest struct {
	FolloweeKSUID string `json:"followee_ksuid" binding:"required" validate:"required"` // 被关注者的用户KSUID
}

// FollowUserResponse 关注用户响应
type FollowUserResponse struct {
	Success       bool   `json:"success"`        // 操作是否成功
	Message       string `json:"message"`        // 响应消息
	FolloweeKSUID string `json:"followee_ksuid"` // 被关注者的用户KSUID
	IsFollowing   bool   `json:"is_following"`   // 是否已关注
}

// UserFollowInfo 用户关注信息
type UserFollowInfo struct {
	UserKSUID      string    `json:"user_ksuid"`      // 用户KSUID
	Username       string    `json:"username"`        // 用户名
	Nickname       string    `json:"nickname"`        // 昵称
	AvatarURL      string    `json:"avatar_url"`      // 头像URL
	UserType       string    `json:"user_type"`       // 用户类型
	IsVerified     bool      `json:"is_verified"`     // 是否认证
	FollowedAt     time.Time `json:"followed_at"`     // 关注时间
	FollowingCount int64     `json:"following_count"` // 关注数
	FanCount       int       `json:"fan_count"`       // 粉丝数
}

// GetFollowersRequest 获取粉丝列表请求
type GetFollowersRequest struct {
	UserKSUID string `form:"user_ksuid" binding:"omitempty"`        // 用户KSUID，如果为空则获取当前用户的粉丝
	Page      int    `form:"page" binding:"min=1" validate:"min=1"` // 页码，从1开始
	PageSize  int    `form:"page_size" binding:"min=1,max=50"`      // 每页数量，最大100
}

// GetFollowingRequest 获取关注列表请求
type GetFollowingRequest struct {
	UserKSUID string `form:"user_ksuid"`                            // 用户KSUID，如果为空则获取当前用户的关注
	Page      int    `form:"page" binding:"min=1" validate:"min=1"` // 页码，从1开始
	PageSize  int    `form:"page_size" binding:"min=1,max=100"`     // 每页数量，最大100
}

// FollowListResponse 关注/粉丝列表响应
type FollowListResponse struct {
	Users      []UserFollowInfo `json:"users"`       // 用户列表
	Total      int64            `json:"total"`       // 总数
	Page       int              `json:"page"`        // 当前页码
	PageSize   int              `json:"page_size"`   // 每页数量
	TotalPages int              `json:"total_pages"` // 总页数
}

// CheckFollowStatusRequest 检查关注状态请求
type CheckFollowStatusRequest struct {
	FolloweeKSUID string `form:"followee_ksuid" binding:"required"` // 被关注者的用户KSUID
}

// CheckFollowStatusResponse 检查关注状态响应
type CheckFollowStatusResponse struct {
	IsFollowing    bool `json:"is_following"`     // 是否已关注
	IsFollowedBy   bool `json:"is_followed_by"`   // 是否被对方关注
	IsMutualFollow bool `json:"is_mutual_follow"` // 是否互相关注
}

// FollowStatsResponse 关注统计响应
type FollowStatsResponse struct {
	FollowingCount int64 `json:"following_count"` // 关注数
	FollowersCount int64 `json:"followers_count"` // 粉丝数
}

// BatchCheckFollowRequest 批量检查关注状态请求
type BatchCheckFollowRequest struct {
	UserKSUIDs []string `json:"user_ksuids" binding:"required,max=100" validate:"required,max=100"` // 用户KSUID列表，最多100个
}

// UserFollowStatus 用户关注状态
type UserFollowStatus struct {
	UserKSUID      string `json:"user_ksuid"`       // 用户KSUID
	IsFollowing    bool   `json:"is_following"`     // 是否已关注
	IsFollowedBy   bool   `json:"is_followed_by"`   // 是否被对方关注
	IsMutualFollow bool   `json:"is_mutual_follow"` // 是否互相关注
}

// BatchCheckFollowResponse 批量检查关注状态响应
type BatchCheckFollowResponse struct {
	FollowStatuses []UserFollowStatus `json:"follow_statuses"` // 关注状态列表
}
